{"compilerOptions": {"baseUrl": ".", "inlineSourceMap": true, "inlineSources": true, "module": "ESNext", "target": "ES6", "allowJs": true, "noImplicitAny": true, "moduleResolution": "node", "importHelpers": true, "isolatedModules": true, "strictNullChecks": true, "lib": ["DOM", "ES5", "ES6", "ES7"], "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "drizzle.config.ts", "__mocks__"]}