[{"sql": ["-- Custom SQL migration file, put you code below! --\nCREATE EXTENSION IF NOT EXISTS vector;"], "bps": true, "folderMillis": 1729509950412, "hash": "6c20ce195f8fc8f8ccb136af42a856f90c3e62e0727ad2e9cd1f01e59efc3a86"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"vector_data_text_embedding_3_small\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(1536),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE TABLE IF NOT EXISTS \"vector_data_text_embedding_3_large\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(3072),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE INDEX IF NOT EXISTS \"embeddingIndex_text_embedding_3_small\" ON \"vector_data_text_embedding_3_small\" USING hnsw (\"embedding\" vector_cosine_ops);"], "bps": true, "folderMillis": 1729509994653, "hash": "30520313039892c9c07b13185b6e4aa0b0f9a09b851db96e0f6e400303560aec"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"vector_data_nomic_embed_text\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(768),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE INDEX IF NOT EXISTS \"embeddingIndex_nomic_embed_text\" ON \"vector_data_nomic_embed_text\" USING hnsw (\"embedding\" vector_cosine_ops);"], "bps": true, "folderMillis": 1729890971064, "hash": "2041cd1e2808ad7ceea75ab34088b1dae1d563286b35d36586129ab6467fb627"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"vector_data_mxbai_embed_large\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(1024),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE INDEX IF NOT EXISTS \"embeddingIndex_mxbai_embed_large\" ON \"vector_data_mxbai_embed_large\" USING hnsw (\"embedding\" vector_cosine_ops);"], "bps": true, "folderMillis": 1729928816942, "hash": "4dd533fe5e978bc0ad79708c073fc3ce973702b7c6eed71f0e4818b1063212fb"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"vector_data_bge_m3\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(1024),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE INDEX IF NOT EXISTS \"embeddingIndex_bge_m3\" ON \"vector_data_bge_m3\" USING hnsw (\"embedding\" vector_cosine_ops);"], "bps": true, "folderMillis": 1730085029605, "hash": "e41dde00d7d98da74596c60e796aa78d5cbfec57208347c72f16a6fa11c24ef1"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"template\" (\n\t\"id\" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,\n\t\"name\" text NOT NULL,\n\t\"content\" jsonb NOT NULL,\n\t\"created_at\" timestamp DEFAULT now() NOT NULL,\n\t\"updated_at\" timestamp DEFAULT now() NOT NULL,\n\tCONSTRAINT \"template_name_unique\" UNIQUE(\"name\")\n);\n"], "bps": true, "folderMillis": 1730443763982, "hash": "eddf72b8d40619c170b3c12f3d3ce280385b1fc05717f211ab6077c6bea691bf"}, {"sql": ["ALTER TABLE \"vector_data_text_embedding_3_small\" RENAME TO \"vector_data_openai_text_embedding_3_small\";", "\nALTER TABLE \"vector_data_text_embedding_3_large\" RENAME TO \"vector_data_openai_text_embedding_3_large\";", "\nALTER TABLE \"vector_data_nomic_embed_text\" RENAME TO \"vector_data_ollama_nomic_embed_text\";", "\nALTER TABLE \"vector_data_mxbai_embed_large\" RENAME TO \"vector_data_ollama_mxbai_embed_large\";", "\nALTER TABLE \"vector_data_bge_m3\" RENAME TO \"vector_data_ollama_bge_m3\";", "\nALTER INDEX IF EXISTS \"embeddingIndex_text_embedding_3_small\" RENAME TO \"embeddingIndex_openai_text_embedding_3_small\";", "\nALTER INDEX IF EXISTS \"embeddingIndex_nomic_embed_text\" RENAME TO \"embeddingIndex_ollama_nomic_embed_text\";", "\nALTER INDEX IF EXISTS \"embeddingIndex_mxbai_embed_large\" RENAME TO \"embeddingIndex_ollama_mxbai_embed_large\";", "\nALTER INDEX IF EXISTS \"embeddingIndex_bge_m3\" RENAME TO \"embeddingIndex_ollama_bge_m3\";", ""], "bps": true, "folderMillis": 1732064682864, "hash": "3dad96e7f1c939c5da3cae4792782e7a35488d39da418e6a9e6f60eafd637f55"}, {"sql": ["CREATE TABLE IF NOT EXISTS \"vector_data_gemini_text_embedding_004\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"embedding\" vector(768),\n\t\"metadata\" jsonb NOT NULL\n);\n", "\nCREATE INDEX IF NOT EXISTS \"embeddingIndex_gemini_text_embedding_004\" ON \"vector_data_gemini_text_embedding_004\" USING hnsw (\"embedding\" vector_cosine_ops);"], "bps": true, "folderMillis": 1732189839252, "hash": "9eef3295787aab3582954d7b8fbdeda90de062f43e09a0a53d839c601c00fe0f"}, {"sql": ["CREATE TABLE \"embeddings\" (\n\t\"id\" serial PRIMARY KEY NOT NULL,\n\t\"path\" text NOT NULL,\n\t\"mtime\" bigint NOT NULL,\n\t\"content\" text NOT NULL,\n\t\"model\" text NOT NULL,\n\t\"dimension\" smallint NOT NULL,\n\t\"embedding\" vector,\n\t\"metadata\" jsonb NOT NULL\n);\n", "\n\nCREATE INDEX \"embeddings_path_index\" ON \"embeddings\" USING btree (\"path\");", "\nCREATE INDEX \"embeddings_model_index\" ON \"embeddings\" USING btree (\"model\");", "\nCREATE INDEX \"embeddings_dimension_index\" ON \"embeddings\" USING btree (\"dimension\");", "\nCREATE INDEX \"embeddings_embedding_768_index\" ON \"embeddings\" USING hnsw ((embedding::vector(768)) vector_cosine_ops) WHERE dimension = 768;", "\nCREATE INDEX \"embeddings_embedding_1024_index\" ON \"embeddings\" USING hnsw ((embedding::vector(1024)) vector_cosine_ops) WHERE dimension = 1024;", "\nCREATE INDEX \"embeddings_embedding_1536_index\" ON \"embeddings\" USING hnsw ((embedding::vector(1536)) vector_cosine_ops) WHERE dimension = 1536;", "\n\n-- Migrate data from openai-text-embedding-3-small\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'openai/text-embedding-3-small', 1536, \"embedding\", \"metadata\"\nFROM \"vector_data_openai_text_embedding_3_small\";", "\n\n-- Migrate data from openai-text-embedding-3-large\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'openai/text-embedding-3-large', 3072, \"embedding\", \"metadata\"\nFROM \"vector_data_openai_text_embedding_3_large\";", "\n\n-- Migrate data from gemini-text-embedding-004\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'gemini/text-embedding-004', 768, \"embedding\", \"metadata\"\nFROM \"vector_data_gemini_text_embedding_004\";", "\n\n-- Migrate data from nomic-embed-text\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'ollama/nomic-embed-text', 768, \"embedding\", \"metadata\"\nFROM \"vector_data_ollama_nomic_embed_text\";", "\n\n-- Migrate data from mxbai-embed-large\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'ollama/mxbai-embed-large', 1024, \"embedding\", \"metadata\"\nFROM \"vector_data_ollama_mxbai_embed_large\";", "\n\n-- Migrate data from bge-m3\nINSERT INTO \"embeddings\" (\"path\", \"mtime\", \"content\", \"model\", \"dimension\", \"embedding\", \"metadata\")\nSELECT \"path\", \"mtime\", \"content\", 'ollama/bge-m3', 1024, \"embedding\", \"metadata\"\nFROM \"vector_data_ollama_bge_m3\";", "\n\nDROP TABLE \"vector_data_openai_text_embedding_3_small\" CASCADE;", "\nDROP TABLE \"vector_data_openai_text_embedding_3_large\" CASCADE;", "\nDROP TABLE \"vector_data_gemini_text_embedding_004\" CASCADE;", "\nDROP TABLE \"vector_data_ollama_nomic_embed_text\" CASCADE;", "\nDROP TABLE \"vector_data_ollama_mxbai_embed_large\" CASCADE;", "\nDROP TABLE \"vector_data_ollama_bge_m3\" CASCADE;\n"], "bps": true, "folderMillis": 1738138268694, "hash": "01ff79bbbba4e3a9dda7a5dc47faf9b649b1fef812897ef714dbf99cdc787efb"}, {"sql": ["CREATE INDEX \"embeddings_embedding_128_index\" ON \"embeddings\" USING hnsw ((embedding::vector(128)) vector_cosine_ops) WHERE dimension = 128;", "\nCREATE INDEX \"embeddings_embedding_256_index\" ON \"embeddings\" USING hnsw ((embedding::vector(256)) vector_cosine_ops) WHERE dimension = 256;", "\nCREATE INDEX \"embeddings_embedding_384_index\" ON \"embeddings\" USING hnsw ((embedding::vector(384)) vector_cosine_ops) WHERE dimension = 384;", "\nCREATE INDEX \"embeddings_embedding_512_index\" ON \"embeddings\" USING hnsw ((embedding::vector(512)) vector_cosine_ops) WHERE dimension = 512;", "\nCREATE INDEX \"embeddings_embedding_1280_index\" ON \"embeddings\" USING hnsw ((embedding::vector(1280)) vector_cosine_ops) WHERE dimension = 1280;", "\nCREATE INDEX \"embeddings_embedding_1792_index\" ON \"embeddings\" USING hnsw ((embedding::vector(1792)) vector_cosine_ops) WHERE dimension = 1792; "], "bps": true, "folderMillis": 1738720225813, "hash": "18a862af0fda7dc9e56e7122a65964a760410272de2188e777e2e0f035d7814f"}]